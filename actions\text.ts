import { ActionState, StatusCode } from "@/lib/types";
import { createText, updateText } from "@/queries/text";
import { revalidatePath } from "next/cache";
import { Text } from "@prisma/client";

type CreateTextProps = {
  sectionId: Text["sectionId"];
  content?: Text["content"];
};

export const createTextAction = async ({
  sectionId,
  content,
}: CreateTextProps): Promise<ActionState<Text>> => {
  try {
    const text = await createText({ sectionId, content });
    return {
      code: StatusCode.Created,
      message: "Text created successfully",
      data: text as Text,
      success: true,
    };
  } catch (error) {
    console.error("Error creating text:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the text",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${sectionId}`);
  }
};

export const updateTextAction = async ({
  id,
  content,
}: {
  id: Text["id"];
  content: Text["content"];
}): Promise<ActionState<Text>> => {
  try {
    const text = await updateText({ id, content });
    return {
      code: StatusCode.Ok,
      message: "Text updated successfully",
      data: text as Text,
      success: true,
    };
  } catch (error) {
    console.error("Error updating text:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the text",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${id}`);
  }
};
